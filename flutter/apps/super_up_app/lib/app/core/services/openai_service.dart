// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:dart_openai/dart_openai.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class OpenAIService {
  static final OpenAIService _instance = OpenAIService._internal();
  factory OpenAIService() => _instance;
  OpenAIService._internal();

  static const String _apiKey =
      "********************************************************************************************************************************************************************";

  bool _isInitialized = false;
  bool _isWebSearchEnabled = false;

  // Conversation history storage - maps roomId to list of messages
  final Map<String, List<OpenAIChatCompletionChoiceMessageModel>>
      _conversationHistory = {};

  void initialize() {
    if (!_isInitialized) {
      OpenAI.apiKey = _apiKey;
      _isInitialized = true;
      if (kDebugMode) {
        print('OpenAI Service initialized');
      }
    }
  }

  // Web search toggle methods
  void enableWebSearch() {
    _isWebSearchEnabled = true;
    if (kDebugMode) {
      print('Web search enabled');
    }
  }

  void disableWebSearch() {
    _isWebSearchEnabled = false;
    if (kDebugMode) {
      print('Web search disabled');
    }
  }

  bool get isWebSearchEnabled => _isWebSearchEnabled;

  // Clear conversation history for a specific room
  void clearConversationHistory(String roomId) {
    _conversationHistory.remove(roomId);
    if (kDebugMode) {
      print('Cleared conversation history for room: $roomId');
    }
  }

  // Clear all conversation history
  void clearAllConversationHistory() {
    _conversationHistory.clear();
    if (kDebugMode) {
      print('Cleared all conversation history');
    }
  }

  Future<String> sendMessage(String message, {String? roomId}) async {
    try {
      if (kDebugMode) {
        print('OpenAI sendMessage called with: "$message"');
      }

      if (!_isInitialized) {
        initialize();
      }

      // Check if API key is set
      if (_apiKey == "YOUR_OPENAI_API_KEY_HERE") {
        return "Please configure your OpenAI API key in the OpenAIService class.";
      }

      // Use default roomId if not provided
      final conversationId = roomId ?? 'default';

      // Get or create conversation history for this room
      if (!_conversationHistory.containsKey(conversationId)) {
        _conversationHistory[conversationId] = [];

        // Add system message only for new conversations
        final systemPrompt = _isWebSearchEnabled
            ? "You are a helpful, intelligent AI assistant with web search capabilities. When users ask questions that require current information, recent data, or real-time facts (like locations, addresses, current events, etc.), search the web to provide accurate, up-to-date information. Always give specific, accurate information - never use placeholder text or templates. Be friendly, concise, and genuinely helpful. Respond as if you're having a real conversation with a friend."
            : "You are a helpful, intelligent AI assistant. Provide natural, conversational responses. Always give specific, accurate information - never use placeholder text or templates. When asked about dates, times, or current information, use the context provided. Be friendly, concise, and genuinely helpful. Respond as if you're having a real conversation with a friend.";

        final systemMessage = OpenAIChatCompletionChoiceMessageModel(
          content: [
            OpenAIChatCompletionChoiceMessageContentItemModel.text(
                systemPrompt),
          ],
          role: OpenAIChatMessageRole.system,
        );

        _conversationHistory[conversationId]!.add(systemMessage);
      }

      // Add current date context to the message
      final now = DateTime.now();
      final currentDate = "${now.day}/${now.month}/${now.year}";
      final currentDay = _getDayName(now.weekday);
      final contextualMessage = "Today is $currentDay, $currentDate. $message";

      final userMessage = OpenAIChatCompletionChoiceMessageModel(
        content: [
          OpenAIChatCompletionChoiceMessageContentItemModel.text(
              contextualMessage),
        ],
        role: OpenAIChatMessageRole.user,
      );

      // Add user message to conversation history
      _conversationHistory[conversationId]!.add(userMessage);

      // Limit conversation history to last 20 messages to avoid token limits
      if (_conversationHistory[conversationId]!.length > 20) {
        // Keep system message and last 19 messages
        final systemMsg = _conversationHistory[conversationId]!.first;
        final recentMessages = _conversationHistory[conversationId]!
            .skip(_conversationHistory[conversationId]!.length - 19)
            .toList();
        _conversationHistory[conversationId] = [systemMsg, ...recentMessages];
      }

      // Create chat completion with web search tools if enabled
      final chatCompletion = _isWebSearchEnabled
          ? await _createChatCompletionWithWebSearch(conversationId)
          : await OpenAI.instance.chat.create(
              model: "gpt-4o",
              messages: _conversationHistory[conversationId]!,
              maxTokens: 500,
              temperature: 0.7,
            );

      final response =
          chatCompletion.choices.first.message.content?.first.text ??
              "I'm sorry, I couldn't process your request at the moment.";

      // Add AI response to conversation history
      final aiMessage = OpenAIChatCompletionChoiceMessageModel(
        content: [
          OpenAIChatCompletionChoiceMessageContentItemModel.text(response),
        ],
        role: OpenAIChatMessageRole.assistant,
      );
      _conversationHistory[conversationId]!.add(aiMessage);

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('OpenAI Error: $e');
      }

      // Return a friendly error message
      if (e.toString().contains('API key')) {
        return "Please configure your OpenAI API key to use the AI assistant.";
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        return "Sorry, I'm having trouble connecting. Please check your internet connection.";
      } else {
        return "Sorry, I'm experiencing some technical difficulties. Please try again later.";
      }
    }
  }

  // Create chat completion with web search tools
  Future<OpenAIChatCompletionModel> _createChatCompletionWithWebSearch(
      String conversationId) async {
    try {
      // Convert conversation history to the format needed for HTTP request
      final messages = _conversationHistory[conversationId]!.map((msg) {
        return {
          "role": msg.role.name,
          "content": msg.content?.first.text ?? "",
        };
      }).toList();

      // Create request body with web search tool
      final requestBody = {
        "model": "gpt-4o",
        "messages": messages,
        "max_tokens": 500,
        "temperature": 0.7,
        "tools": [
          {"type": "web_search"}
        ],
        "tool_choice": "auto", // Let the model decide when to use web search
      };

      if (kDebugMode) {
        print('Making web search enabled request to OpenAI');
      }

      // Make HTTP request with web search tools
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (kDebugMode) {
          print('Web search response received: ${responseData.toString()}');
        }

        // Convert response back to OpenAIChatCompletionModel format
        final choice = responseData['choices'][0];
        final message = choice['message'];
        final content =
            message['content'] ?? "Sorry, I couldn't process your request.";

        // Create a mock OpenAIChatCompletionModel for compatibility
        return OpenAIChatCompletionModel(
          id: responseData['id'] ?? 'web_search_response',
          choices: [
            OpenAIChatCompletionChoiceModel(
              index: 0,
              message: OpenAIChatCompletionChoiceMessageModel(
                role: OpenAIChatMessageRole.assistant,
                content: [
                  OpenAIChatCompletionChoiceMessageContentItemModel.text(
                      content),
                ],
              ),
              finishReason: choice['finish_reason'],
            ),
          ],
          created: responseData['created'] ??
              DateTime.now().millisecondsSinceEpoch ~/ 1000,
          systemFingerprint: responseData['system_fingerprint'],
          usage: OpenAIChatCompletionUsageModel(
            promptTokens: responseData['usage']?['prompt_tokens'] ?? 0,
            completionTokens: responseData['usage']?['completion_tokens'] ?? 0,
            totalTokens: responseData['usage']?['total_tokens'] ?? 0,
          ),
        );
      } else {
        if (kDebugMode) {
          print(
              'Web search request failed with status: ${response.statusCode}');
          print('Response body: ${response.body}');
        }

        // Fallback to regular chat completion
        return await OpenAI.instance.chat.create(
          model: "gpt-4o",
          messages: _conversationHistory[conversationId]!,
          maxTokens: 500,
          temperature: 0.7,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Web search error: $e');
      }

      // Fallback to regular chat completion
      return await OpenAI.instance.chat.create(
        model: "gpt-4o",
        messages: _conversationHistory[conversationId]!,
        maxTokens: 500,
        temperature: 0.7,
      );
    }
  }

  // Method to simulate typing delay for better UX
  Future<void> simulateTyping() async {
    await Future.delayed(const Duration(milliseconds: 1500));
  }

  // Generate image using GPT-4.1 with image generation tool
  Future<String?> generateImage(String prompt) async {
    try {
      if (kDebugMode) {
        print('Generating image with GPT-4.1 and prompt: $prompt');
      }

      // Use the new Responses API with GPT-4.1 and image generation tool
      final requestBody = {
        "model": "gpt-4.1-mini",
        "input": "Generate an image: $prompt",
        "tools": [
          {"type": "image_generation"}
        ],
        "tool_choice": {"type": "image_generation"}, // Force image generation
      };

      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/responses'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final outputs = responseData['output'] as List;

        // Find image generation call in outputs
        for (final output in outputs) {
          if (output['type'] == 'image_generation_call' &&
              output['status'] == 'completed') {
            final imageBase64 = output['result'] as String;

            if (kDebugMode) {
              print('Generated image with GPT-4.1 successfully');
              if (output['revised_prompt'] != null) {
                print('Revised prompt: ${output['revised_prompt']}');
              }
            }

            return imageBase64; // Return base64 encoded image
          }
        }
      } else {
        if (kDebugMode) {
          print('Image generation failed with status: ${response.statusCode}');
          print('Response body: ${response.body}');
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Image generation error: $e');
      }
      return null;
    }
  }

  // Convert speech to text using Whisper API
  Future<String?> speechToText(File audioFile) async {
    try {
      if (kDebugMode) {
        print('Converting speech to text with Whisper API');
      }

      final audioTranscription =
          await OpenAI.instance.audio.createTranscription(
        file: audioFile,
        model: "whisper-1",
        responseFormat: OpenAIAudioResponseFormat.json,
      );

      final transcribedText = audioTranscription.text;

      if (kDebugMode) {
        print('Speech to text result: $transcribedText');
      }

      return transcribedText;
    } catch (e) {
      if (kDebugMode) {
        print('Speech to text error: $e');
      }
      return null;
    }
  }

  // Analyze image using OpenAI Vision API with direct HTTP call
  Future<String?> analyzeImage(File imageFile, String? userPrompt) async {
    try {
      if (kDebugMode) {
        print('Analyzing image with OpenAI Vision API');
      }

      // Read image file as bytes
      final imageBytes = await imageFile.readAsBytes();

      // Convert to base64
      final base64Image = base64Encode(imageBytes);

      // Prepare the text content
      String textContent = userPrompt != null && userPrompt.trim().isNotEmpty
          ? "User's question about this image: ${userPrompt.trim()}"
          : "Please analyze this image and describe what you see in detail.";

      // Create system prompt for image analysis
      String systemPrompt =
          "You are a helpful AI assistant that can analyze images. Describe what you see in the image in detail. Be specific and accurate about objects, people, text, colors, and any other relevant details you observe.";

      // Create the request body with proper format
      final requestBody = {
        "model": "gpt-4o",
        "messages": [
          {"role": "system", "content": systemPrompt},
          {
            "role": "user",
            "content": [
              {"type": "text", "text": textContent},
              {
                "type": "image_url",
                "image_url": {"url": "data:image/jpeg;base64,$base64Image"}
              }
            ]
          }
        ],
        "max_tokens": 500,
        "temperature": 0.7
      };

      // Make the HTTP request
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['choices'][0]['message']['content'];

        if (kDebugMode) {
          print('Image analysis successful: $content');
        }

        return content;
      } else {
        if (kDebugMode) {
          print('Image analysis failed with status: ${response.statusCode}');
          print('Response body: ${response.body}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Image analysis error: $e');
      }
      return null;
    }
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }
}
